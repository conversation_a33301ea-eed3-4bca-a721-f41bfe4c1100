import {
  BrowserRouter as Router,
  Routes,
  Route,
  Navigate,
} from "react-router-dom";
import "./App.css";
import { AuthProvider } from "./contexts/AuthContext";
import { AppProvider } from "./contexts/AppContext";
import { ApiProvider } from "./contexts/ApiContext";
import { ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import Login from "./pages/Login";
import Dashboard from "./pages/Dashboard";
import Anchors from "./pages/Anchors";
import Users from "./pages/Users";
import Roles from "./pages/Roles";
import RoleConfiguration from "./pages/RoleConfiguration";
import SupportTickets from "./pages/SupportTickets";
import NotFound from "./pages/NotFound";
import ProtectedRoute from "./components/ProtectedRoute";
import Regions from "./pages/Regions";
import Sectors from "./pages/Sectors";
import Purposes from "./pages/Purposes";
import Segments from "./pages/Segments";
import Categories from "./pages/Categories";
import Branches from "./pages/Branches";
import Types from "./pages/Types";
import Products from "./pages/Products";
import Customers from "./pages/Customers";
import FollowUps from "./pages/FollowUps";
import Calls from "./pages/Calls";
import Visits from "./pages/Visits";
import GeneralActivities from "./pages/GeneralActivities";
import TwoByTwoByTwoActivities from "./pages/TwoByTwoByTwoActivities";
import Hitlist from "./pages/Hitlist";
import Targets from "./pages/Targets";

function App() {
  return (
    <AuthProvider>
      <AppProvider>
        <ApiProvider>
          <Router>
            <Routes>
              <Route path="/" element={<Navigate to="/login" replace />} />
              <Route path="/login" element={<Login />} />
              <Route
                path="/dashboard"
                element={
                  <ProtectedRoute>
                    <Dashboard />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/anchors"
                element={
                  <ProtectedRoute>
                    <Anchors />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/support-tickets"
                element={
                  <ProtectedRoute>
                    <SupportTickets />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/admin/users"
                element={
                  <ProtectedRoute>
                    <Users />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/admin/roles"
                element={
                  <ProtectedRoute>
                    <Roles />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/admin/roles/create"
                element={
                  <ProtectedRoute>
                    <RoleConfiguration />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/admin/roles/edit/:id"
                element={
                  <ProtectedRoute>
                    <RoleConfiguration />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/admin/targets"
                element={<Navigate to="/admin/targets/role-targets" replace />}
              />
              <Route
                path="/admin/targets/role-targets"
                element={
                  <ProtectedRoute>
                    <Targets />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/admin/targets/individual-targets"
                element={
                  <ProtectedRoute>
                    <Targets />
                  </ProtectedRoute>
                }
              />
              <Route path="*" element={<NotFound />} />
              <Route
                path="/items/regions"
                element={
                  <ProtectedRoute>
                    <Regions />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/items/sectors"
                element={
                  <ProtectedRoute>
                    <Sectors />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/items/purposes"
                element={
                  <ProtectedRoute>
                    <Purposes />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/items/segments"
                element={
                  <ProtectedRoute>
                    <Segments />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/items/categories"
                element={
                  <ProtectedRoute>
                    <Categories />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/items/branches"
                element={
                  <ProtectedRoute>
                    <Branches />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/items/types"
                element={
                  <ProtectedRoute>
                    <Types />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/items/products"
                element={
                  <ProtectedRoute>
                    <Products />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/customers"
                element={
                  <ProtectedRoute>
                    <Customers />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/follow-ups"
                element={
                  <ProtectedRoute>
                    <FollowUps />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/calls"
                element={
                  <ProtectedRoute>
                    <Calls />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/visits"
                element={
                  <ProtectedRoute>
                    <Visits />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/customer-service/hitlist"
                element={
                  <ProtectedRoute>
                    <GeneralActivities />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/customer-service/2by2by2"
                element={
                  <ProtectedRoute>
                    <TwoByTwoByTwoActivities />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/customer-service/2by2by2/:type"
                element={
                  <ProtectedRoute>
                    <TwoByTwoByTwoActivities />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/hitlist"
                element={
                  <ProtectedRoute>
                    <Hitlist />
                  </ProtectedRoute>
                }
              />
              <Route path="*" element={<NotFound />} />
            </Routes>
            <ToastContainer
              position="bottom-right"
              autoClose={4000}
              hideProgressBar={true}
              newestOnTop={true}
              closeOnClick={false}
              rtl={false}
              pauseOnFocusLoss={false}
              draggable={false}
              pauseOnHover={false}
              theme="colored"
              toastClassName={(context) => {
                const baseClass =
                  "relative flex min-h-12 rounded-lg justify-between overflow-hidden cursor-pointer bg-white shadow-lg border-l-4";
                const contextClass = {
                  success: "success-toast",
                  error: "error-toast",
                  warning: "warning-toast",
                  default: "default-toast",
                }[context?.type || "default"];
                return `${baseClass} ${contextClass}`;
              }}
              bodyClassName={() => "text-sm font-medium block p-4"}
              closeButton={false}
            />
          </Router>
        </ApiProvider>
      </AppProvider>
    </AuthProvider>
  );
}

export default App;
