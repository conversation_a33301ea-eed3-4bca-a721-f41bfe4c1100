import { useState } from "react";
import { Link, useLocation } from "react-router-dom";
import { useApp } from "../contexts/AppContext";
import { menuItems, isRouteActive } from "../config/menuItems";
import logoSmall from "../assets/images/logo.png";
import logoText from "../assets/images/logo-text.png";
import { CalendarDays, ChevronRight, Heart } from "lucide-react";
import { getTodaysDate } from "../utils/dateUtils";
import { ArrowLeft, X } from "lucide-react";
import { BsQuestionCircle } from "react-icons/bs";
import { FaRegRectangleList } from "react-icons/fa6";
import {
  MdDashboard,
  MdInventory,
  MdTrackChanges,
  MdPeople,
  MdAnchor,
  MdGroups,
  MdSettings,
  MdSupport,
} from "react-icons/md";

function Sidebar() {
  const { sidebarCollapsed, isMobile } = useApp();
  const location = useLocation();
  const [expandedItems, setExpandedItems] = useState({});

  // Function to check if a menu item is active (supports nested children)
  const isItemActive = (item) => {
    // Use regex-based matching for better route detection
    if (isRouteActive(location.pathname, item)) {
      return true;
    }
    // Check if any child is active (supports nested children)
    if (item.children) {
      return item.children.some((child) => {
        if (isRouteActive(location.pathname, child)) {
          return true;
        }
        // Check nested children
        if (child.children) {
          return child.children.some((grandchild) =>
            isRouteActive(location.pathname, grandchild)
          );
        }
        return false;
      });
    }
    return false;
  };

  const toggleExpanded = (itemId) => {
    setExpandedItems((prev) => ({
      ...prev,
      [itemId]: !prev[itemId],
    }));
  };

  // Recursive function to render menu items with support for multiple levels
  const renderMenuItem = (item, level = 0) => {
    const isExpanded = expandedItems[item.id];
    const hasChildren = item.hasChildren && item.children;
    const isActive = isItemActive(item);

    const linkContent = (
      <>
        <span
          className={`${sidebarCollapsed ? "mx-auto" : "mr-3"} flex-shrink-0`}
        >
          {typeof item.icon === "string" ? (
            item.icon
          ) : item.icon ? (
            <item.icon size={20} className="text-current" />
          ) : level > 0 ? (
            <span className="w-2 h-2 rounded-full bg-current opacity-60"></span>
          ) : null}
        </span>
        {!sidebarCollapsed && (
          <>
            <span className="transition-opacity duration-300">
              {item.label}
            </span>
            {hasChildren && (
              <span
                className={`ml-auto transition-transform ${
                  isExpanded ? "rotate-90" : ""
                }`}
              >
                <ChevronRight size={18} />
              </span>
            )}
          </>
        )}
      </>
    );

    const baseClasses = `flex items-center px-3 py-2 rounded-lg transition-colors duration-200 ${
      sidebarCollapsed ? "justify-center" : ""
    }`;

    const activeClasses =
      level === 0
        ? "text-white bg-[#1c5b41]"
        : "text-[#1c5b41] dark:text-[#1c5b41] font-semibold";

    const inactiveClasses =
      level === 0
        ? "text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800"
        : "text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-800";

    return (
      <li key={item.id}>
        {hasChildren ? (
          <button
            onClick={() => toggleExpanded(item.id)}
            className={`w-full ${baseClasses} ${
              isActive ? activeClasses : inactiveClasses
            } ${level > 0 ? "text-sm" : ""}`}
          >
            {linkContent}
          </button>
        ) : item.href !== "#" ? (
          <Link
            to={item.href}
            className={`${baseClasses} ${
              isActive ? activeClasses : inactiveClasses
            } ${level > 0 ? "text-sm" : ""}`}
          >
            {linkContent}
          </Link>
        ) : (
          <span
            className={`${baseClasses} ${
              isActive ? activeClasses : inactiveClasses
            } ${level > 0 ? "text-sm" : ""}`}
          >
            {linkContent}
          </span>
        )}
        {hasChildren && isExpanded && !sidebarCollapsed && (
          <ul className={`${level === 0 ? "ml-6" : "ml-4"} mt-2 space-y-1`}>
            {item.children.map((child) => renderMenuItem(child, level + 1))}
          </ul>
        )}
      </li>
    );
  };

  // Don't render on mobile - MobileSidebar handles mobile
  if (isMobile) {
    return null;
  }

  return (
    <aside
      id="sidebar"
      className={`${
        sidebarCollapsed ? "w-20" : "w-72"
      } relative bg-white dark:bg-black hidden md:flex flex-col h-full transition-all duration-300 ease-in-out`}
    >
      {/* Logo - Fixed at top */}
      <div className="flex items-center justify-between h-24 px-4">
        <div className="flex items-center gap-4">
          <span className="h-11 flex-shrink-0">
            <img className="logo-abbr h-full w-auto" src={logoSmall} alt="" />
          </span>
          {!sidebarCollapsed && (
            <span className="h-10 transition-opacity duration-300">
              <img
                className="brand-title h-full w-auto"
                src={logoText}
                alt=""
              />
            </span>
          )}
        </div>
      </div>

      {/* Navigation - Scrollable */}
      <nav className="flex-1 overflow-y-auto p-4 scrollbar-thin">
        <ul className="space-y-2">{menuItems.map(renderMenuItem)}</ul>
        {!sidebarCollapsed && (
          <>
            {/* Date widget at bottom - Fixed */}
            <div className="p-4 flex-shrink-0">
              <div className="bg-[#ff6d4c] text-white p-3 relative overflow-hidden rounded-[1rem] h-26 flex items-center transition-colors duration-200">
                <div className="text-[16px] font-medium whitespace-nowrap pl-5">
                  {getTodaysDate()}
                </div>
                <CalendarDays
                  size={120}
                  className="text-gray-200 -rotate-[20deg] absolute -right-[5%] top-[10%] opacity-[0.3]"
                />
                {/* <div className="text-xs opacity-90">localhost/kb/</div> */}
              </div>
            </div>
            <div className="text-left text-gray-500 dark:text-gray-400 pb-4 pl-4">
              <p className="text-sm font-light">
                <strong className="font-normal">KB Tracker</strong> ©{" "}
                {new Date().getFullYear()} All Rights Reserved
              </p>
              <p className="text-xs font-medium flex mt-2">
                Made with
                <Heart size={16} className="mx-1 text-red-500" />
                by Mine Softwares
              </p>
            </div>
          </>
        )}
      </nav>
    </aside>
  );
}

export default Sidebar;
