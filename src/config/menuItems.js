import { BsQuestionCircle } from "react-icons/bs";
import { FaRegRectangleList } from "react-icons/fa6";
import {
  MdDashboard,
  MdPeople,
  MdAnchor,
  MdGroups,
  MdSettings,
  MdSupportAgent,
} from "react-icons/md";

// Centralized menu items configuration for both desktop and mobile sidebars
export const menuItems = [
  {
    id: "dashboard",
    label: "Dashboard",
    icon: MdDashboard,
    href: "/dashboard",
    activePattern: "^/dashboard$",
  },
  {
    id: "items",
    label: "Items",
    icon: FaRegRectangleList,
    href: "#",
    hasChildren: true,
    children: [
      { id: "products", label: "Products", href: "/items/products" },
      { id: "categories", label: "Categories", href: "/items/categories" },
      { id: "segments", label: "Segments", href: "/items/segments" },
      { id: "types", label: "Types", href: "/items/types" },
      { id: "sectors", label: "ISIC Sectors", href: "/items/sectors" },
      { id: "regions", label: "Regions", href: "/items/regions" },
      { id: "branches", label: "Branches", href: "/items/branches" },
      { id: "purpose", label: "Purposes", href: "/items/purposes" },
    ],
  },
  {
    id: "hitlist",
    label: "Hit list",
    icon: MdPeople,
    href: "/hitlist",
    activePattern: "^/hitlist$",
  },
  {
    id: "customer-service",
    label: "Customer Service",
    icon: MdSupportAgent,
    href: "#",
    hasChildren: true,
    children: [
      { id: "hitlist", label: "Hitlist", href: "/customer-service/hitlist", activePattern: "^/customer-service/hitlist" },
      { id: "2by2by2-activities", label: "2 by 2 by 2 Activities", href: "/customer-service/2by2by2", activePattern: "^/customer-service/2by2by2" },
    ],
  },
  {
    id: "anchors",
    label: "Anchors",
    icon: MdAnchor,
    href: "/anchors",
    activePattern: "^/anchors",
  },
  {
    id: "customers",
    label: "Customers",
    icon: MdGroups,
    href: "/customers",
  },
  {
    id: "administration",
    label: "Administration",
    icon: MdSettings,
    href: "",
    hasChildren: true,
    children: [
      { id: "users", label: "Users", href: "/admin/users", activePattern: "^/admin/users" },
      { id: "roles", label: "Roles", href: "/admin/roles", activePattern: "^/admin/roles" },
      {
        id: "targets",
        label: "Targets",
        href: "/admin/targets",
        activePattern: "^/admin/targets",
        hasChildren: true,
        children: [
          { id: "role-targets", label: "Role Targets", href: "/admin/targets/role-targets", activePattern: "^/admin/targets/role-targets" },
          { id: "individual-targets", label: "Individual Targets", href: "/admin/targets/individual-targets", activePattern: "^/admin/targets/individual-targets" },
        ]
      },
      // { id: "permissions", label: "Permissions", href: "#" },
    ],
  },
  {
    id: "support-tickets",
    label: "Support Tickets",
    icon: BsQuestionCircle,
    href: "/support-tickets",
    activePattern: "^/support-tickets",
  },
];

// Helper function to check if a route is active using regex patterns
export const isRouteActive = (pathname, item) => {
  // If item has activePattern, use regex matching
  if (item.activePattern) {
    const regex = new RegExp(item.activePattern);
    return regex.test(pathname);
  }

  // Fallback to exact match
  return item.href === pathname;
};

// Helper function to get page title from route
export const getPageTitleFromRoute = (pathname) => {
  // First check direct matches
  const directMatch = menuItems.find(item => item.href === pathname);
  if (directMatch) {
    return directMatch.label;
  }

  // Check children for matches
  for (const item of menuItems) {
    if (item.children) {
      const childMatch = item.children.find(child => child.href === pathname);
      if (childMatch) {
        return childMatch.label;
      }
    }
  }

  // Check for role configuration pages
  if (pathname === "/admin/roles/create") {
    return "Create Role";
  }
  if (pathname.startsWith("/admin/roles/edit/")) {
    return "Edit Role";
  }

  // Check for targets pages
  if (pathname === "/admin/targets") {
    return "Targets";
  }
  if (pathname === "/admin/targets/role-targets") {
    return "Role Targets";
  }
  if (pathname === "/admin/targets/individual-targets") {
    return "Individual Targets";
  }

  // Check for hitlist page
  if (pathname === "/hitlist") {
    return "Hit list";
  }

  // Check for customer service pages
  if (pathname === "/customer-service/hitlist") {
    return "Customer Service - Hitlist";
  }
  if (pathname === "/customer-service/2by2by2" || pathname === "/customer-service/2by2by2/hitlist") {
    return "Customer Service - 2 by 2 by 2 Activities";
  }
  if (pathname === "/customer-service/2by2by2/first2") {
    return "Customer Service - First 2";
  }
  if (pathname === "/customer-service/2by2by2/second2") {
    return "Customer Service - Second 2";
  }
  if (pathname === "/customer-service/2by2by2/third2") {
    return "Customer Service - Third 2";
  }

  // Check for login page
  if (pathname === "/login") {
    return "Login";
  }

  // Default fallback
  return "Dashboard";
};
